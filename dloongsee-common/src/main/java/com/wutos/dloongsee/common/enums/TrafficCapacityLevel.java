package com.wutos.dloongsee.common.enums;

import lombok.Getter;

/**
 * 道路通行能力等级
 */
@Getter
public enum TrafficCapacityLevel {
    A("自由流","正常监控"),
    B("稳定流","动态情报板提醒基本畅通"),
    C("稳定流","分流引导，增加收费站窗口"),
    D("稳定流","分流引导，收费站管控"),
    E("不稳定流","物理管控，开放临时车道"),
    F("强制流","启动重大拥堵处置预案");

    private final String label;
    private final String suggestion;

    TrafficCapacityLevel(String label,String suggestion) {
        this.label = label;
        this.suggestion = suggestion;
    }

    //vc比获取通行能力等级
    public static TrafficCapacityLevel getLevel(double vc) {
        if (vc <= 0.35) {
            return TrafficCapacityLevel.A;
        } else if (vc <= 0.55) {
            return TrafficCapacityLevel.B;
        } else if (vc <= 0.75) {
            return TrafficCapacityLevel.C;
        } else if (vc <= 0.9) {
            return TrafficCapacityLevel.D;
        } else if (vc <= 1.0) {
            return TrafficCapacityLevel.E;
        } else {
            return TrafficCapacityLevel.F;
        }
    }
}
