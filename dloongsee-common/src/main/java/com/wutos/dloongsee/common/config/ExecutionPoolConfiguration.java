package com.wutos.dloongsee.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ExecutionPoolConfiguration {


    @Bean(name = "zmqDataPersistenceThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor zmqDataPersistenceThreadPoolTaskExecutor() {
        return getThreadPoolTaskExecutor(300, 20, 50, 1000, "zmq-data-persistence-");
    }

    @Bean(name = "carTrackThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor carTrackThreadPoolTaskExecutor() {
        return getThreadPoolTaskExecutor(300, 50, 50, 1000, "car-track-");
    }

    @Bean(name = "eventThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor eventThreadPoolTaskExecutor() {
        return getThreadPoolTaskExecutor(300, 10, 50, 1000, "event-");
    }

    @Bean(name = "trafficCapacityThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor trafficCapacityThreadPoolTaskExecutor() {
        return getThreadPoolTaskExecutor(300, 10, 50, 1000, "traffic-capacity-");
    }

    @Bean(name = "replayThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor replayThreadPoolTaskExecutor() {
        return getThreadPoolTaskExecutor(300, 10, 50, 1000, "replay-");
    }

    public static ThreadPoolTaskExecutor getThreadPoolTaskExecutor(int keepAliveSeconds, int corePoolSize, int maxPoolSize, int queueCapacity, String threadName) {
        ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
        pool.setKeepAliveSeconds(keepAliveSeconds);
        pool.setCorePoolSize(corePoolSize);//核心线程池数
        pool.setMaxPoolSize(maxPoolSize); // 最大线程
        pool.setQueueCapacity(queueCapacity);//队列容量
        pool.setThreadNamePrefix(threadName);
        pool.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy()); //队列满，线程被拒绝执行策略

        return pool;
    }
}
