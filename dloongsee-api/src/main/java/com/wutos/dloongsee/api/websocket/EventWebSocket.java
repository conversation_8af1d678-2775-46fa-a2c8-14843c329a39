package com.wutos.dloongsee.api.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wutos.dloongsee.api.components.GeoComponent;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.service.RoadService;
import com.wutos.dloongsee.api.vo.RtEventVO;
import com.wutos.dloongsee.common.dto.Lnglat;
import com.wutos.dloongsee.common.dto.ZMQEventDTO;
import com.wutos.dloongsee.common.springevent.EventWsOpenEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventWebSocket extends TextWebSocketHandler implements WebSocketConfigurer {
    private static final String ROUTE = "/rtEvent";
    // 存储每个路段ID对应的WebSocket会话列表
    private static final Map<Integer, CopyOnWriteArrayList<WebSocketSession>> roadSegmentSessions = new ConcurrentHashMap<>();

    @Autowired
    private RoadService roadService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private GeoComponent geoComponent;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    private Map<Integer, RoadSegment> ROAD_SEGMENTS;
    private List<Lnglat> roadLnglats;

    @PostConstruct
    public void init() {
        ROAD_SEGMENTS = roadService.getRoadSegments().stream().collect(Collectors.toMap(RoadSegment::getId, e -> e));
        // 经纬度
        roadLnglats = roadService.getAllRoadSegmentDefine().stream().flatMap(e -> e.getLnglats().stream()).collect(Collectors.toList());
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(this, ROUTE).setAllowedOrigins("*");
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String query = session.getUri().getQuery();
        Map<String, Integer> params = parseQuery(query);
        Integer roadSegmentId = params.get("roadSegmentId");
        session.getAttributes().put("roadSegmentId", roadSegmentId);
        roadSegmentSessions.computeIfAbsent(roadSegmentId, k -> new CopyOnWriteArrayList<>()).add(session);
        log.info("实时事件websocket连接建立成功，路段ID：{}", roadSegmentId);
        applicationEventPublisher.publishEvent(new EventWsOpenEvent(session));
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        Integer roadSegmentId = (Integer) session.getAttributes().get("roadSegmentId");
        CopyOnWriteArrayList<WebSocketSession> sessions = roadSegmentSessions.get(roadSegmentId);
        if (sessions != null) {
            sessions.remove(session);
            if (sessions.isEmpty()) {
                roadSegmentSessions.remove(roadSegmentId);
            }
            log.info("实时事件websocket连接关闭，路段ID：{}, 剩余连接数：{}", roadSegmentId, sessions.size());
        }
    }

    /**
     * 向客户端发送消息
     */
    public void sendMessage(List<ZMQEventDTO> zmqEventDtoList, WebSocketSession session) {
        try {
            if (session == null) {
                for (Integer roadSegmentId : roadSegmentSessions.keySet()) {
                    List<RtEventVO> eventVOList = toVO(zmqEventDtoList, roadSegmentId);
                    for (WebSocketSession s : roadSegmentSessions.get(roadSegmentId)) {
                        s.sendMessage(new TextMessage(objectMapper.writeValueAsString(eventVOList)));
                    }
                }
            } else {
                List<RtEventVO> eventVOList = toVO(zmqEventDtoList, (Integer) session.getAttributes().get("roadSegmentId"));
                session.sendMessage(new TextMessage(objectMapper.writeValueAsString(eventVOList)));
            }
        } catch (Exception e) {
            log.error("发送实时事件消息失败", e);
        }
    }

    private List<RtEventVO> toVO(List<ZMQEventDTO> zmqEventDtoList, Integer roadSegmentId) {
        List<RtEventVO> eventVOList;
        if (roadSegmentId != -1) {
            RoadSegment roadSegment = ROAD_SEGMENTS.get(roadSegmentId);
            Integer segStart = roadSegment.getStartMil();
            Integer segEnd = roadSegment.getEndMil();
            eventVOList = zmqEventDtoList.stream()
                    .filter(eventDto -> {
                        Integer start = eventDto.getStartMil();
                        Integer end = eventDto.getEndMil();
                        if (end == null) {
                            return start >= segStart && start <= segEnd;
                        }
                        // end不为null, 判断是否和路段有交集
                        return end >= segStart && start <= segEnd;
                    })
                    .map(eventDto -> {
                        Integer start = Math.max(eventDto.getStartMil(), segStart);
                        Integer end = eventDto.getEndMil() == null ? null : Math.min(eventDto.getEndMil(), segEnd);
                        return RtEventVO.builder()
                                .eventId(eventDto.getEventId())
                                .carId(eventDto.getCarId())
                                .carNum(eventDto.getCarNum())
                                .wn(eventDto.getLane())
                                .startTime(eventDto.getStartTime())
                                .endTime(eventDto.getEndTime())
                                .startMil(start)
                                .endMil(end)
                                .updateTime(eventDto.getUpdateTime())
                                .direction(eventDto.getRoadDirection())
                                .type(eventDto.getType())
                                .speed(eventDto.getSpeed())
                                .level(eventDto.getLevel())
                                .category(eventDto.getType().getCategory())
                                .typeName(eventDto.getType().getLabel())
                                .build();
                    })
                    .collect(Collectors.toList());
        } else {
            eventVOList = zmqEventDtoList.stream()
                    .map(eventDto -> {
                        double[] startArr = geoComponent.milToLngLat(eventDto.getStartMil());
                        Lnglat startLnglat = new Lnglat(startArr[0], startArr[1]);
                        Lnglat endLnglat = null;
                        List<Lnglat> lnglats = new ArrayList<>();
                        lnglats.add(startLnglat);
                        if (eventDto.getEndMil() != null) {
                            double[] endArr = geoComponent.milToLngLat(eventDto.getEndMil());
                            endLnglat = new Lnglat(endArr[0], endArr[1]);
                            double minLat = Math.min(startLnglat.getLat(), endLnglat.getLat());
                            double maxLat = Math.max(startLnglat.getLat(), endLnglat.getLat());
                            List<Lnglat> filterLnglats = roadLnglats.stream().filter(e -> e.getLat() >= minLat && e.getLat() <= maxLat).collect(Collectors.toList());
                            lnglats.addAll(filterLnglats);
                            lnglats.add(endLnglat);
                        }
                        return RtEventVO.builder()
                                .eventId(eventDto.getEventId())
                                .carId(eventDto.getCarId())
                                .carNum(eventDto.getCarNum())
                                .wn(eventDto.getLane())
                                .startTime(eventDto.getStartTime())
                                .endTime(eventDto.getEndTime())
                                .startMil(eventDto.getStartMil())
                                .endMil(eventDto.getEndMil())
                                .updateTime(eventDto.getUpdateTime())
                                .direction(eventDto.getRoadDirection())
                                .type(eventDto.getType())
                                .speed(eventDto.getSpeed())
                                .startLnglat(startLnglat)
                                .endLnglat(endLnglat)
                                .level(eventDto.getLevel())
                                .category(eventDto.getType().getCategory())
                                .typeName(eventDto.getType().getLabel())
                                .segmentId(roadService.getRoadSegmentByMil(eventDto.getStartMil()) == null ? null : roadService.getRoadSegmentByMil(eventDto.getStartMil()).getId())
                                .lnglats(lnglats)
                                .build();
                    })
                    .collect(Collectors.toList());
        }
        return eventVOList;
    }

    private Map<String, Integer> parseQuery(String query) {
        Map<String, Integer> map = new HashMap<>();
        if (query != null) {
            for (String param : query.split("&")) {
                String[] pair = param.split("=");
                if (pair.length > 1) {
                    map.put(pair[0], Integer.parseInt(pair[1]));
                }
            }
        }
        return map;
    }
}
