package com.wutos.dloongsee.api.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wutos.dloongsee.api.components.GeoComponent;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.mock.MockBigCarWeightService;
import com.wutos.dloongsee.api.service.CarTrackService;
import com.wutos.dloongsee.api.service.RoadService;
import com.wutos.dloongsee.api.vo.RtCarTrackVO;
import com.wutos.dloongsee.common.dto.Lnglat;
import com.wutos.dloongsee.common.dto.ZMQCarTrackDto;
import com.wutos.dloongsee.common.enums.RampType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CarTrackWebsocket extends TextWebSocketHandler implements WebSocketConfigurer {
    private static final String ROUTE = "/rtTrace";
    // 存储每个路段ID对应的WebSocket会话列表
    private static final Map<Integer, CopyOnWriteArrayList<WebSocketSession>> roadSegmentSessions = new ConcurrentHashMap<>();

    @Autowired
    private RoadService roadService;
    @Autowired
    private GeoComponent geoComponent;
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MockBigCarWeightService mockBigCarWeightService;

    private Map<Integer, RoadSegment> ROAD_SEGMENTS;

    @PostConstruct
    public void init() {
        ROAD_SEGMENTS = roadService.getRoadSegments().stream().collect(Collectors.toMap(RoadSegment::getId, e -> e));
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(this, ROUTE).setAllowedOrigins("*");
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String query = session.getUri().getQuery();
        Map<String, Integer> params = parseQuery(query);
        Integer roadSegmentId = params.get("roadSegmentId");
        session.getAttributes().put("roadSegmentId", roadSegmentId);
        roadSegmentSessions.computeIfAbsent(roadSegmentId, k -> new CopyOnWriteArrayList<>()).add(session);
        log.info("实时轨迹websocket连接建立成功，路段ID：{}", roadSegmentId);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        Integer roadSegmentId = (Integer) session.getAttributes().get("roadSegmentId");
        CopyOnWriteArrayList<WebSocketSession> sessions = roadSegmentSessions.get(roadSegmentId);
        if (sessions != null) {
            sessions.remove(session);
            if (sessions.isEmpty()) {
                roadSegmentSessions.remove(roadSegmentId);
            }
            log.info("实时轨迹websocket连接关闭，路段ID：{}, 剩余连接数：{}", roadSegmentId, sessions.size());
        }
    }

    /**
     * 向所有客户端发送消息
     */
    public void sendMessage(List<ZMQCarTrackDto> zmqCarTrackDtoList) {
        try {
            for (Integer roadSegmentId : roadSegmentSessions.keySet()) {
                List<RtCarTrackVO> carTrackVOs;
                if (roadSegmentId != -1) {
                    //TODO 新增车辆模拟轴距、车重数据，用于演示(清除缓存)
                    final AtomicBoolean hasOverWeight = new AtomicBoolean(false);
                    List<String> carIds = zmqCarTrackDtoList.stream().map(ZMQCarTrackDto::getId).collect(Collectors.toList());
                    mockBigCarWeightService.clearCarAttributesCache(carIds);

                    carTrackVOs = zmqCarTrackDtoList.stream()
                            .filter(zmqCarTrackDto ->
                                    zmqCarTrackDto.getMil() >= ROAD_SEGMENTS.get(roadSegmentId).getStartMil() && zmqCarTrackDto.getMil() <= ROAD_SEGMENTS.get(roadSegmentId).getEndMil())
                            .map(zmqCarTrackDto -> {
                                int mil = zmqCarTrackDto.getMil();
                                RoadDirection direction = zmqCarTrackDto.getRoadDirection();
                                Integer rampId = null;
                                if (zmqCarTrackDto.isGetOut()) {
                                    rampId = CarTrackService.getNearPosition(mil, direction, RampType.OUT).getId();
                                }
                                if (zmqCarTrackDto.isComeIn()) {
                                    rampId = CarTrackService.getNearPosition(mil, direction, RampType.IN).getId();
                                }

                                //TODO 新增车辆模拟轴距、车重数据，用于演示
                                Integer axlesNum = null;
                                Double carWeight = null;
                                if (roadSegmentId == 2) {
                                    // 获取或生成车辆的固定属性
                                    MockBigCarWeightService.CarFixedAttributes attributes = mockBigCarWeightService.getOrGenerateCarAttributes(
                                            zmqCarTrackDto.getId(),
                                            zmqCarTrackDto.getCarType(),
                                            hasOverWeight
                                    );
                                    axlesNum = attributes.getAxlesNum();
                                    carWeight = attributes.getCarWeight();
                                }

                                return RtCarTrackVO.builder()
                                        .id(zmqCarTrackDto.getId())
                                        .cn(zmqCarTrackDto.getCn())
                                        .type(zmqCarTrackDto.getCarType())
                                        .wn(zmqCarTrackDto.getWn())
                                        .mil(mil)
                                        .speed(zmqCarTrackDto.getSpeed())
                                        .direction(direction)
                                        .getOut(zmqCarTrackDto.isGetOut())
                                        .comeIn(zmqCarTrackDto.isComeIn())
                                        .rampId(rampId)
                                        .messageTicks(zmqCarTrackDto.getMessageTicks())
                                        .axlesNum(axlesNum)
                                        .carWeight(carWeight)
                                        .build();
                            })
                            .collect(Collectors.toList());
                } else {
                    carTrackVOs = zmqCarTrackDtoList.stream()
                            .filter(zmqCarTrackDto -> geoComponent.milToLngLat(zmqCarTrackDto.getMil()) != null)
                            .map(zmqCarTrackDto -> {
                                double[] lngLatArr = geoComponent.milToLngLat(zmqCarTrackDto.getMil());
                                return RtCarTrackVO.builder()
                                        .id(zmqCarTrackDto.getId())
                                        .wn(zmqCarTrackDto.getWn())
                                        .type(zmqCarTrackDto.getCarType())
                                        .direction(zmqCarTrackDto.getRoadDirection())
                                        .lnglat(new Lnglat(lngLatArr[0], lngLatArr[1]))
                                        .messageTicks(zmqCarTrackDto.getMessageTicks())
                                        .build();
                            })
                            .collect(Collectors.toList());
                }
                for (WebSocketSession session : roadSegmentSessions.get(roadSegmentId)) {
                    session.sendMessage(new TextMessage(objectMapper.writeValueAsString(carTrackVOs)));
                }
            }
        } catch (Exception e) {
            log.error("发送实时轨迹消息失败", e);
        }
    }

    private Map<String, Integer> parseQuery(String query) {
        Map<String, Integer> map = new HashMap<>();
        if (query != null) {
            for (String param : query.split("&")) {
                String[] pair = param.split("=");
                if (pair.length > 1) {
                    map.put(pair[0], Integer.parseInt(pair[1]));
                }
            }
        }
        return map;
    }
}
