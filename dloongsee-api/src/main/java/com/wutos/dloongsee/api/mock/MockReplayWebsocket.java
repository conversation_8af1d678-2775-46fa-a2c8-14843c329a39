package com.wutos.dloongsee.api.mock;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.service.CarTrackService;
import com.wutos.dloongsee.api.service.RoadService;
import com.wutos.dloongsee.api.vo.RtCarTrackVO;
import com.wutos.dloongsee.common.dto.ZMQCarTrackDto;
import com.wutos.dloongsee.common.enums.RampType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MockReplayWebsocket extends TextWebSocketHandler implements WebSocketConfigurer {
    private static final String ROUTE = "/mock/replay";
    // 每个 session 对应一个任务
    private final Map<WebSocketSession, ScheduledFuture<?>> tasks = new ConcurrentHashMap<>();

    @Autowired
    private RoadService roadService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ThreadPoolTaskExecutor replayThreadPoolTaskExecutor;
    @Autowired
    private TaskScheduler taskScheduler;

    @Value("classpath:mock/up_send_2025-09-09_11.log")
    private Resource resource;

    private Map<Integer, RoadSegment> ROAD_SEGMENTS;

    @PostConstruct
    public void init() {
        ROAD_SEGMENTS = roadService.getRoadSegments().stream().collect(Collectors.toMap(RoadSegment::getId, e -> e));
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(this, ROUTE).setAllowedOrigins("*");
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String query = session.getUri().getQuery();
        Map<String, String> params = parseQuery(query);
        log.info("回放websocket连接建立成功");

        replayThreadPoolTaskExecutor.execute(() -> {
            try {
                // 查询轨迹
                String s = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
                List<List<Map<String, Object>>> carData = objectMapper.readValue(s, new TypeReference<List<List<Map<String, Object>>>>() {
                });
                List<List<ZMQCarTrackDto>> zmqCarTrackDtoList = carData.stream()
                        .map(innerList -> innerList.stream()
                                .map(map -> ZMQCarTrackDto.builder()
                                        .id(map.get("car_id") + "@" + RoadDirection.DOWN)
                                        .cn("鄂A0000" + map.get("car_id"))
                                        .wn(((Number) map.get("lane")).intValue())
                                        .mil(((Number) map.get("pos")).intValue() + ROAD_SEGMENTS.get(5).getStartMil())
                                        .speed(((Number) map.get("speed")).floatValue())
                                        .direction(1)
                                        .type(((Number) map.get("car_type")).intValue())
                                        .updateTime(LocalDateTime.now())
                                        .messageTicks(((Number) map.get("timestamp")).longValue())
                                        .getOut(false)
                                        .comeIn(false)
                                        .build())
                                .collect(Collectors.toList()))
                        .collect(Collectors.toList());

                // 降频
                List<List<ZMQCarTrackDto>> zmqCarTrackDtos = new ArrayList<>();
                long timestamp = 0;
                for (int i = 0; i < zmqCarTrackDtoList.size(); i++) {
                    List<ZMQCarTrackDto> frame = zmqCarTrackDtoList.get(i);
                    long currentTicks = frame.get(0).getMessageTicks();
                    if (i == 0 || currentTicks - timestamp >= 1000) {
                        timestamp = currentTicks;
                        zmqCarTrackDtos.add(frame);
                    }
                }

                // 定时发送
                startSend(session, zmqCarTrackDtos);
            } catch (Exception e) {
                log.error("发送回放轨迹失败", e);
                try {
                    session.close();
                } catch (IOException ex) {
                    log.error("回放websocket连接关闭失败", e);
                }
            }
        });
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        stopSend(session);
        log.info("回放websocket连接关闭");
    }

    private void startSend(WebSocketSession session, List<List<ZMQCarTrackDto>> zmqCarTrackDtoList) {
        if (zmqCarTrackDtoList.isEmpty()) {
            // 任务结束
            stopSend(session);
            return;
        }
        AtomicInteger index = new AtomicInteger(0);
        ScheduledFuture<?> future = taskScheduler.scheduleAtFixedRate(() -> {
            int i = index.getAndIncrement();
            if (i >= zmqCarTrackDtoList.size()) {
                // 任务结束
                sendMessage(session, Collections.emptyList(), 5);
                stopSend(session);
                return;
            }
            List<ZMQCarTrackDto> frame = zmqCarTrackDtoList.get(i);
            sendMessage(session, frame, 5);
        }, 1000);
        tasks.put(session, future);
    }

    private void stopSend(WebSocketSession session) {
        ScheduledFuture<?> future = tasks.remove(session);
        if (future != null) {
            future.cancel(true);
        }
    }

    /**
     * 向客户端发送消息
     */
    public void sendMessage(WebSocketSession session, List<ZMQCarTrackDto> zmqCarTrackDtoList, Integer roadSegmentId) {
        try {
            List<RtCarTrackVO> carTrackVOs = zmqCarTrackDtoList.stream()
                    .filter(zmqCarTrackDto ->
                            zmqCarTrackDto.getMil() >= ROAD_SEGMENTS.get(roadSegmentId).getStartMil() && zmqCarTrackDto.getMil() <= ROAD_SEGMENTS.get(roadSegmentId).getEndMil())
                    .map(zmqCarTrackDto -> {
                        int mil = zmqCarTrackDto.getMil();
                        RoadDirection direction = zmqCarTrackDto.getRoadDirection();
                        Integer rampId = null;
                        if (zmqCarTrackDto.isGetOut()) {
                            rampId = CarTrackService.getNearPosition(mil, direction, RampType.OUT).getId();
                        }
                        if (zmqCarTrackDto.isComeIn()) {
                            rampId = CarTrackService.getNearPosition(mil, direction, RampType.IN).getId();
                        }
                        return RtCarTrackVO.builder()
                                .id(zmqCarTrackDto.getId())
                                .cn(zmqCarTrackDto.getCn())
                                .type(zmqCarTrackDto.getCarType())
                                .wn(zmqCarTrackDto.getWn())
                                .mil(mil)
                                .speed(zmqCarTrackDto.getSpeed())
                                .direction(direction)
                                .getOut(zmqCarTrackDto.isGetOut())
                                .comeIn(zmqCarTrackDto.isComeIn())
                                .rampId(rampId)
                                .messageTicks(zmqCarTrackDto.getMessageTicks())
                                .focus(Objects.equals(zmqCarTrackDto.getId(), "2@" + direction))
                                .build();
                    })
                    .collect(Collectors.toList());
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(carTrackVOs)));
        } catch (Exception e) {
            log.error("发送回放轨迹消息失败", e);
        }
    }

    private Map<String, String> parseQuery(String query) {
        Map<String, String> map = new HashMap<>();
        if (query != null) {
            for (String param : query.split("&")) {
                String[] pair = param.split("=");
                if (pair.length > 1) {
                    map.put(pair[0], pair[1]);
                }
            }
        }
        return map;
    }
}
