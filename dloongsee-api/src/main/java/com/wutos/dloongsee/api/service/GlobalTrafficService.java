package com.wutos.dloongsee.api.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wutos.dloongsee.api.config.TrafficCapacityConfig;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.entity.TrafficCapacity;
import com.wutos.dloongsee.api.entity.TrafficCapacitySegment;
import com.wutos.dloongsee.api.entity.TrafficCapacitySummary;
import com.wutos.dloongsee.api.mapper.TrafficCapacityMapper;
import com.wutos.dloongsee.api.mapper.TrafficCapacitySegmentMapper;
import com.wutos.dloongsee.api.mapper.TrafficCapacitySummaryMapper;
import com.wutos.dloongsee.api.vo.ControlSuggestionVO;
import com.wutos.dloongsee.api.vo.GlobalTrafficAvgTravelSpeedLineVO;
import com.wutos.dloongsee.api.vo.GlobalTrafficAvgTravelTimeBarVO;
import com.wutos.dloongsee.api.vo.GlobalTrafficFlowAnalysisVO;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.enums.TrafficCapacityLevel;
import com.wutos.dloongsee.common.utils.TaskTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GlobalTrafficService {

    @Autowired
    private TrafficCapacityMapper trafficCapacityMapper;

    @Autowired
    private TrafficCapacitySegmentMapper trafficCapacitySegmentMapper;

    @Autowired
    private TrafficCapacityConfig config;

    @Autowired
    private TrafficCapacitySummaryMapper trafficCapacitySummaryMapper;

    @Autowired
    private RoadService roadService;

    public GlobalTrafficFlowAnalysisVO getTrafficFlowAnalysis() {
        LambdaQueryWrapper<TrafficCapacitySummary> upQueryWrapper = new LambdaQueryWrapper<>();
        upQueryWrapper.eq(TrafficCapacitySummary::getSegmentId, -1)
                .eq(TrafficCapacitySummary::getDirection, RoadDirection.UP)
                .orderByDesc(TrafficCapacitySummary::getCreateTime)
                .last("limit 10");
        List<TrafficCapacitySummary> upTrafficCapacities = trafficCapacitySummaryMapper.selectList(upQueryWrapper);

        LambdaQueryWrapper<TrafficCapacitySummary> downQueryWrapper = new LambdaQueryWrapper<>();
        downQueryWrapper.eq(TrafficCapacitySummary::getSegmentId, -1)
                .eq(TrafficCapacitySummary::getDirection, RoadDirection.DOWN)
                .orderByDesc(TrafficCapacitySummary::getCreateTime)
                .last("limit 10");
        List<TrafficCapacitySummary> downTrafficCapacities = trafficCapacitySummaryMapper.selectList(downQueryWrapper);

        List<String> times = upTrafficCapacities.stream().map(capacity -> DateUtil.format(capacity.getCreateTime(), "HH:mm"))
                .collect(Collectors.toList());
        //转换为veh/h
        List<Integer> upCarFlow = upTrafficCapacities.stream()
                .map(capacity ->
                        (int) (capacity.getNum() / (config.getDuration() / 3600.0)))
                .collect(Collectors.toList());
        List<Integer> downCarFlow = downTrafficCapacities.stream()
                .map(capacity -> (int) (capacity.getNum() / (config.getDuration() / 3600.0)))
                .collect(Collectors.toList());

        return GlobalTrafficFlowAnalysisVO.builder()
                .times(times)
                .upCarFlow(upCarFlow)
                .downCarFlow(downCarFlow)
                .build();
    }

    public GlobalTrafficAvgTravelSpeedLineVO getAvgTravelSpeedLine() {
        LambdaQueryWrapper<TrafficCapacitySegment> segmentQueryWrapper = new LambdaQueryWrapper<>();
        segmentQueryWrapper.orderByAsc(TrafficCapacitySegment::getStartMil);
        List<Integer> milPoints = trafficCapacitySegmentMapper.selectList(segmentQueryWrapper)
                .stream().map(TrafficCapacitySegment::getStartMil).collect(Collectors.toList());

        //获取最近一次计算的时间，来获取上下行速度行程曲线
        LocalDateTime lastExecutionTime = TaskTimeUtils.getLastExecutionTime(LocalDateTime.now(), config.getDuration());

        LambdaQueryWrapper<TrafficCapacity> upQueryWrapper = new LambdaQueryWrapper<>();
        upQueryWrapper.eq(TrafficCapacity::getDirection, RoadDirection.UP)
                .eq(TrafficCapacity::getCreateTime, lastExecutionTime)
                .orderByAsc(TrafficCapacity::getStartMil);
        List<Double> upSpeeds = trafficCapacityMapper.selectList(upQueryWrapper)
                .stream().map(capacity -> capacity.getSpeedAvg() * 3.6).collect(Collectors.toList());

        LambdaQueryWrapper<TrafficCapacity> downQueryWrapper = new LambdaQueryWrapper<>();
        downQueryWrapper.eq(TrafficCapacity::getDirection, RoadDirection.DOWN)
                .eq(TrafficCapacity::getCreateTime, lastExecutionTime)
                .orderByAsc(TrafficCapacity::getStartMil);
        List<Double> downSpeeds = trafficCapacityMapper.selectList(downQueryWrapper)
                .stream().map(capacity -> capacity.getSpeedAvg() * 3.6).collect(Collectors.toList());

        return GlobalTrafficAvgTravelSpeedLineVO.builder()
                .milPoints(milPoints)
                .upSpeeds(upSpeeds)
                .downSpeeds(downSpeeds)
                .build();
    }


    public GlobalTrafficAvgTravelTimeBarVO getAvgTravelTimeBar() {
        //获取最近一次计算的时间，来获取上下行行程时间
        LocalDateTime lastExecutionTime = TaskTimeUtils.getLastExecutionTime(LocalDateTime.now(), config.getDuration());

        List<TrafficCapacitySummary> summaries = trafficCapacitySummaryMapper.selectList(Wrappers.lambdaQuery(TrafficCapacitySummary.class)
                .ne(TrafficCapacitySummary::getSegmentId, -1)
                .eq(TrafficCapacitySummary::getCreateTime, lastExecutionTime));

        List<String> roadSegments = new ArrayList<>();
        List<Double> upTimes = new ArrayList<>();
        List<Double> downTimes = new ArrayList<>();

        for (RoadSegment roadSegment : roadService.getRoadSegments()) {
            roadSegments.add(roadSegment.getName());
            double upTime = summaries.stream()
                    .filter(e -> e.getDirection() == RoadDirection.UP && Objects.equals(e.getSegmentId(), roadSegment.getId()))
                    .mapToDouble(TrafficCapacitySummary::getTimeAvg).sum();
            double downTime = summaries.stream()
                    .filter(e -> e.getDirection() == RoadDirection.DOWN && Objects.equals(e.getSegmentId(), roadSegment.getId()))
                    .mapToDouble(TrafficCapacitySummary::getTimeAvg).sum();
            upTimes.add(upTime / 60);
            downTimes.add(downTime / 60);
        }

        return GlobalTrafficAvgTravelTimeBarVO.builder()
                .roadSegments(roadSegments)
                .upTimes(upTimes)
                .downTimes(downTimes)
                .build();
    }


    public List<ControlSuggestionVO> getControlSuggestions() {
        List<ControlSuggestionVO> resultList = new ArrayList<>();
        //获取最近一次计算的时间，来获取上下行行程时间
        LocalDateTime lastExecutionTime = TaskTimeUtils.getLastExecutionTime(LocalDateTime.now(), config.getDuration());
        LambdaQueryWrapper<TrafficCapacitySummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TrafficCapacitySummary::getCreateTime, lastExecutionTime);
        queryWrapper.ne(TrafficCapacitySummary::getSegmentId, -1);
        List<TrafficCapacitySummary> trafficCapacityList = trafficCapacitySummaryMapper.selectList(queryWrapper);
        trafficCapacityList.forEach(e -> {
            ControlSuggestionVO controlSuggestionVO = new ControlSuggestionVO();
            TrafficCapacityLevel level = TrafficCapacityLevel.getLevel(e.getVc());
            Optional<RoadSegment> roadSegmentOptional = roadService.getRoadSegments().stream().filter(segment -> segment.getId().equals(e.getSegmentId())).findFirst();
            roadSegmentOptional.ifPresent(roadSegment -> controlSuggestionVO.setSegmentName(roadSegment.getName()));
            controlSuggestionVO.setDirection(e.getDirection());
            controlSuggestionVO.setVc(e.getVc());
            controlSuggestionVO.setSegmentId(e.getSegmentId());
            controlSuggestionVO.setServiceLevelLabel(level.getLabel());
            controlSuggestionVO.setSuggestion(level.getSuggestion());
            controlSuggestionVO.setLevel(level);
            resultList.add(controlSuggestionVO);
        });
        return resultList.stream().sorted(Comparator.comparing(ControlSuggestionVO::getVc).reversed()).collect(Collectors.toList());
    }

}
