package com.wutos.dloongsee.api.vo;

import com.wutos.dloongsee.common.dto.Lnglat;
import com.wutos.dloongsee.common.enums.CarType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.Builder;
import lombok.Data;

/**
 * 实时轨迹
 */
@Data
@Builder
public class RtCarTrackVO {

    private String id;

    private String cn;

    private CarType type;

    private Integer wn;

    private Integer mil;

    private Float speed;

    private RoadDirection direction;

    private Boolean getOut;

    private Boolean comeIn;

    private Lnglat lnglat;

    private Integer rampId;

    private long messageTicks;

    // 车辆轴数
    private Integer axlesNum;

    // 车重（单位：吨）
    private Double carWeight;

    /**
     * 重点关注
     */
    @Builder.Default
    private Boolean focus = false;
}
