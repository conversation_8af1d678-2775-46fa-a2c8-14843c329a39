package com.wutos.dloongsee.api.mock;

import com.wutos.dloongsee.common.enums.CarType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
public class MockBigCarWeightService {

    private static final Map<String, CarFixedAttributes> carAttributesCache = new ConcurrentHashMap<>();

    @Getter
    @AllArgsConstructor
    public static class CarFixedAttributes {
        private final Integer axlesNum;
        private final Double carWeight;
    }

    public CarFixedAttributes getOrGenerateCarAttributes(String carId, CarType carType, AtomicBoolean hasOverWeight) {
        // 只为大车生成轴数和车重数据
        if (carType != CarType.BIG) {
            return new CarFixedAttributes(null, null);
        }

        // 从缓存中获取已存在的属性
        CarFixedAttributes existingAttributes = carAttributesCache.get(carId);
        if (existingAttributes != null) {
            return existingAttributes;
        }

        // 生成新的轴数和车重数据
        Integer axlesNum = (int) (Math.random() * 4) + 3; // 3-6轴随机
        Double carWeight;

        // 根据轴数分配车重范围
        if (axlesNum == 3) {
            carWeight = 20 + Math.floor(7 * Math.random()); // 20-26吨
        } else if (axlesNum == 4) {
            carWeight = 25 + Math.floor(10 * Math.random()); // 25-34吨
        } else if (axlesNum == 5) {
            carWeight = 30 + Math.floor(12 * Math.random()); // 30-41吨
        } else { // 6轴
            carWeight = 30 + Math.floor(18 * Math.random()); // 30-47吨
        }

        // 特殊处理：如果还没有超重车辆，将当前车辆设为超重
        if (!hasOverWeight.get()) {
            carWeight = 49 + Math.floor(10 * Math.random()); // 49-58吨
            hasOverWeight.set(true);
        }

        // 创建新的属性对象并缓存
        CarFixedAttributes newAttributes = new CarFixedAttributes(axlesNum, carWeight);
        carAttributesCache.put(carId, newAttributes);

        return newAttributes;
    }

    /**
     * 清理车辆属性缓存
     * @param carIds
     */
    public void clearCarAttributesCache(List<String> carIds) {
        carAttributesCache.keySet().removeIf(carIds::contains);
    }
}
