# 车辆轴数车重缓存修复说明

## 问题描述

在原有的WebSocket车辆追踪代码中，当路段ID为2时，系统会为大车（CarType.BIG）随机生成轴数和车重数据。但存在一个问题：每次车辆数据更新时，同一辆车的轴数和车重都会重新随机生成，导致同一辆车的这些属性不断变化，不符合实际情况。

## 解决方案

### 1. 核心修改

在 `CarTrackWebsocket.java` 中实现了车辆固定属性缓存机制：

#### 1.1 添加缓存数据结构
```java
// 车辆固定属性缓存：存储每辆车的轴数和车重信息
private static final Map<String, CarFixedAttributes> carAttributesCache = new ConcurrentHashMap<>();

// 车辆固定属性内部类
private static class CarFixedAttributes {
    private final Integer axlesNum;
    private final Double carWeight;
    // ... 构造方法和getter方法
}
```

#### 1.2 属性生成和缓存方法
```java
private CarFixedAttributes getOrGenerateCarAttributes(String carId, CarType carType, AtomicBoolean hasOverWeight) {
    // 只为大车生成轴数和车重数据
    if (carType != CarType.BIG) {
        return new CarFixedAttributes(null, null);
    }
    
    // 从缓存中获取已存在的属性
    CarFixedAttributes existingAttributes = carAttributesCache.get(carId);
    if (existingAttributes != null) {
        return existingAttributes;
    }
    
    // 生成新的轴数和车重数据并缓存
    // ...
}
```

#### 1.3 修改sendMessage方法
将原有的随机生成逻辑替换为：
```java
// 获取或生成车辆的固定属性
CarFixedAttributes attributes = getOrGenerateCarAttributes(
        zmqCarTrackDto.getId(), 
        zmqCarTrackDto.getCarType(), 
        hasOverWeight
);
axlesNum = attributes.getAxlesNum();
carWeight = attributes.getCarWeight();
```

### 2. 功能特性

#### 2.1 属性一致性保证
- 同一辆车（通过车辆ID标识）的轴数和车重在首次生成后保持不变
- 使用 `ConcurrentHashMap` 确保线程安全

#### 2.2 保持原有随机逻辑
- **轴数**：3-6轴随机生成
- **车重根据轴数分配**：
  - 3轴：20-26吨
  - 4轴：25-34吨  
  - 5轴：30-41吨
  - 6轴：30-47吨
- **超重车辆特殊设置**：49-58吨（每批数据中最多一辆）

#### 2.3 缓存管理
提供了缓存管理方法：
```java
// 清理指定车辆的缓存
public void clearCarAttributesCache(String carId)

// 清理所有缓存
public void clearCarAttributesCache(null)

// 获取当前缓存的车辆数量
public int getCachedCarCount()
```

### 3. 使用说明

#### 3.1 正常使用
修改后的代码无需额外配置，会自动：
1. 为新出现的大车生成固定的轴数和车重
2. 为已存在的车辆返回缓存的属性值
3. 小车不生成轴数和车重数据（保持null）

#### 3.2 缓存清理（可选）
如果需要定期清理缓存以防止内存泄漏，可以：
```java
@Autowired
private CarTrackWebsocket carTrackWebsocket;

// 清理特定车辆
carTrackWebsocket.clearCarAttributesCache("1@UP");

// 清理所有缓存
carTrackWebsocket.clearCarAttributesCache(null);

// 查看缓存大小
int count = carTrackWebsocket.getCachedCarCount();
```

### 4. 测试验证

创建了测试类 `CarTrackWebsocketTest.java` 来验证：
1. 车辆类型识别正确性
2. 轴数和车重范围的正确性
3. 缓存机制的基本逻辑

### 5. 技术细节

#### 5.1 线程安全
- 使用 `ConcurrentHashMap` 保证多线程环境下的安全性
- 属性对象使用 `final` 字段确保不可变性

#### 5.2 内存管理
- 缓存使用车辆ID作为key，理论上会随着车辆数量增长
- 提供了清理方法，可根据业务需要定期清理
- 建议在生产环境中监控缓存大小

#### 5.3 兼容性
- 完全兼容原有代码逻辑
- 只在路段ID为2且车辆类型为大车时生效
- 不影响其他路段或小车的处理逻辑

## 总结

此修复确保了同一辆车的轴数和车重属性在整个追踪过程中保持一致，解决了原有代码中属性不断变化的问题，同时保持了原有的随机生成逻辑和特殊处理规则。
