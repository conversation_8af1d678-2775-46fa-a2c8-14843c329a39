package com.wutos.dloongsee.external.inbound;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CarTrackInbound {

    /**
     * 包序列号
     */
    private int sn;

    /**
     * 服务ip
     */
    private String deviceIp;

    /**
     * 时间戳
     */
    private long timestamp;

    /**
     * 车辆数量
     */
    private int count;

    private List<CarInfoDto> carInfoDtoList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CarInfoDto {

        /**
         * 车辆id
         */
        private long carId;

        /**
         * 车牌号
         */
        private String carNumber;

        /**
         * 车辆类型（0小型车，1大车）
         */
        private int carType;

        /**
         * 车辆影响里程范围
         */
        private int[] scope;

        /**
         * 车速
         */
        private float speed;

        /**
         * 车道号
         */
        private int wayNo;

        /**
         * 里程
         */
        private int mileage;

        /**
         * 预留字段
         */
        private int extend1;

        /**
         * 方向
         */
        private int direct;

    }
}
