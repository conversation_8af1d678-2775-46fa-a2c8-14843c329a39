package com.wutos.dloongsee.external;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wutos.dloongsee.common.components.InfluxdbComponent;
import com.wutos.dloongsee.common.dto.ZMQCarTrackDto;
import com.wutos.dloongsee.common.springevent.ZMQCarTrackEvent;
import com.wutos.dloongsee.external.convertor.CarTrackConvertor;
import com.wutos.dloongsee.external.entity.ZMQCarTrack;
import com.wutos.dloongsee.external.inbound.CarTrackInbound;
import com.wutos.dloongsee.external.properties.ZMQTrackProperties;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.Point;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.zeromq.ZMQ.Socket;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.wutos.dloongsee.common.utils.ParseUtils.*;

@Slf4j
@Service
@EnableConfigurationProperties({ZMQTrackProperties.class})
public class ZMQTrackProcessor {

    private final ZMQTrackProperties zmqTrackProperties;

    private final InfluxdbComponent influxdbComponent;

    private final ThreadPoolTaskExecutor zmqDataPersistenceThreadPoolTaskExecutor;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final ObjectMapper objectMapper;

    public ZMQTrackProcessor(ZMQTrackProperties zmqTrackProperties, InfluxdbComponent influxdbComponent, ThreadPoolTaskExecutor zmqDataPersistenceThreadPoolTaskExecutor, ApplicationEventPublisher applicationEventPublisher, ObjectMapper objectMapper) {
        this.zmqTrackProperties = zmqTrackProperties;
        this.influxdbComponent = influxdbComponent;
        this.zmqDataPersistenceThreadPoolTaskExecutor = zmqDataPersistenceThreadPoolTaskExecutor;
        this.applicationEventPublisher = applicationEventPublisher;
        this.objectMapper = objectMapper;
    }

    @PostConstruct
    private void processZMQTrack() {
        zmqTrackProperties.getServers().forEach(zmqServer -> {
            Socket socket = ZMQUtils.connect(zmqServer.getUrl(), zmqServer.getTopic());
            Thread thread = new Thread(() -> {
                while (!Thread.currentThread().isInterrupted()) {
                    byte[] bytes = socket.recv();
                    if (bytes != null && bytes.length > 0) {
//                        zmqDataPersistenceThreadPoolTaskExecutor.execute(() -> persistTrackMetaData(bytes, zmqServer.getDirection()));

                        CarTrackInbound carTrackInbound = extractToCarTrackDto(bytes);
                        log.debug("ZMQ接收到track数据: {}", carTrackInbound);
                        List<ZMQCarTrackDto> zmqCarTrackDtoList = carTrackInbound.getCarInfoDtoList().stream()
                                .map(carInfoDto -> CarTrackConvertor.inboundToOutbound(carInfoDto, carTrackInbound.getTimestamp(), zmqServer.getDirection()))
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());

                        if (!zmqCarTrackDtoList.isEmpty()) {
                            try {
                                applicationEventPublisher.publishEvent(new ZMQCarTrackEvent(zmqCarTrackDtoList));
                            } catch (Exception e) {
                                log.error("发送车辆轨迹数据失败" + ": {}", zmqCarTrackDtoList, e);
                            }
                        }
                        zmqDataPersistenceThreadPoolTaskExecutor.execute(() -> persistTrackData(zmqCarTrackDtoList));
                    }
                }
            }, "zmq-track-process-" + zmqServer.getDirection());
            thread.start();
        });
    }

    private void persistTrackData(List<ZMQCarTrackDto> zmqCarTrackDtoList) {
        List<Point> points = new ArrayList<>();
        for (int i = 0; i < zmqCarTrackDtoList.size(); i++) {
            ZMQCarTrack zmqCarTrack = CarTrackConvertor.outBoundToTb(zmqCarTrackDtoList.get(i));
            Point point = Point.measurement("tb_car_track")
                    .tag("Index", i + "@" + zmqCarTrackDtoList.get(i).getId().split("@")[1])
                    .tag("Direction", String.valueOf(zmqCarTrack.getDirection()))
                    .time(zmqCarTrack.getMessageTicks(), TimeUnit.MILLISECONDS)
                    .addField("CarLicense", zmqCarTrack.getCarLicense())
                    .addField("CarLine", zmqCarTrack.getCarLine())
                    .addField("CarSpeed", zmqCarTrack.getCarSpeed())
                    .addField("CarType", zmqCarTrack.getCarType())
                    .addField("Id", zmqCarTrack.getId())
                    .addField("MessageTicks", zmqCarTrack.getMessageTicks())
                    .addField("UpdateTime", DateUtil.formatLocalDateTime(zmqCarTrackDtoList.get(i).getUpdateTime()))
                    .addField("Mil", zmqCarTrack.getMil())
                    .build();
            points.add(point);
        }
        try {
            influxdbComponent.insertByPoints(points);
        } catch (Exception e) {
            log.error("存储车辆轨迹数据失败: {}", points.size(), e);
        }
    }

    private void persistTrackMetaData(byte[] bytes, String direction) {
        Point point = Point.measurement("tb_car_track_metadata")
                .tag("direction", direction)
                .addField("data", Base64.getEncoder().encodeToString(bytes))
                .build();
        influxdbComponent.insertByPoint(point);
    }

    private CarTrackInbound extractToCarTrackDto(byte[] bytes) {
        byte[] deviceIpBytes = Arrays.copyOfRange(bytes, 4, 8);
        String deviceIp = IntStream.range(0, deviceIpBytes.length)
                .mapToObj(i -> String.valueOf(deviceIpBytes[i] & 0xFF))
                .collect(Collectors.joining(","));

        int carCount = byteToInt(bytes, 16, 20);
        List<CarTrackInbound.CarInfoDto> carInfoDtoList = extractCars(bytes, carCount);

        return CarTrackInbound.builder()
                .sn(byteToInt(bytes, 0, 4))
                .deviceIp(deviceIp)
                .timestamp(byteToLong(bytes, 8, 16))
                .count(carCount)
                .carInfoDtoList(carInfoDtoList)
                .build();
    }

    private List<CarTrackInbound.CarInfoDto> extractCars(byte[] bytes, int carCount) {
        int header = 20;
        int carLength = 44;
        return IntStream.range(0, carCount)
                .mapToObj(i -> {
                    long carId = byteToLong(bytes, header + carLength * i, header + carLength * i + 8);
                    String carNumber = new String(Arrays.copyOfRange(bytes, header + carLength * i + 8, header + carLength * i + 24), StandardCharsets.UTF_8).trim();
                    carNumber = carNumber.isEmpty() ? "未识别车牌" : carNumber;
                    carNumber = "无车牌".equals(carNumber) ? "未识别车牌" : carNumber;
                    int carType = bytes[header + carLength * i + 24];
                    int[] scope = new int[]{byteToInt(bytes, header + carLength * i + 25, header + carLength * i + 29),
                            byteToInt(bytes, header + carLength * i + 29, header + carLength * i + 33)};
                    float speed = byteToFloat(bytes, header + carLength * i + 33, header + carLength * i + 37);
                    int wayNo = bytes[header + carLength * i + 37];
                    int mileage = byteToInt(bytes, header + carLength * i + 38, header + carLength * i + 42);
                    int extend1 = bytes[header + carLength * i + 42];
                    int direct = bytes[header + carLength * i + 43];
                    return CarTrackInbound.CarInfoDto.builder()
                            .carId(carId)
                            .carNumber(carNumber)
                            .carType(carType)
                            .scope(scope)
                            .speed(speed)
                            .wayNo(wayNo)
                            .mileage(mileage)
                            .extend1(extend1)
                            .direct(direct)
                            .build();
                }).collect(Collectors.toList());

    }

}
